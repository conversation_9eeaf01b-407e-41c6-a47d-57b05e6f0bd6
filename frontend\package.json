{"name": "pystation-web-frontend", "version": "1.0.0", "description": "PyStation Web Frontend - Vue 3 + TypeScript", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "axios": "^1.5.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.1.0", "echarts": "^5.4.0", "vue-echarts": "^6.6.0"}, "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.0", "typescript": "~5.2.0", "vite": "^4.4.0", "vue-tsc": "^1.8.0"}, "engines": {"node": ">=16.0.0"}}