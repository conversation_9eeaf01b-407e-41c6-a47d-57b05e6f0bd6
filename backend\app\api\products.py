"""
产品管理API路由
"""

from typing import List
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.user import User
from app.models.product import Product
from app.schemas.product import Product as ProductSchema, ProductCreate, ProductUpdate, ProductList
from app.core.security import get_current_active_user
from app.core.exceptions import not_found_exception, bad_request_exception

router = APIRouter()


@router.get("/", response_model=ProductList)
async def get_products(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取产品列表"""
    products = db.query(Product).offset(skip).limit(limit).all()
    total = db.query(Product).count()
    
    return ProductList(products=products, total=total)


@router.get("/{product_id}", response_model=ProductSchema)
async def get_product(
    product_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取单个产品"""
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise not_found_exception("产品不存在")
    
    return product


@router.post("/", response_model=ProductSchema)
async def create_product(
    product_data: ProductCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """创建产品"""
    # 检查产品名是否已存在
    existing_product = db.query(Product).filter(Product.name == product_data.name).first()
    if existing_product:
        raise bad_request_exception("产品名已存在")
    
    # 创建新产品
    db_product = Product(**product_data.dict())
    db.add(db_product)
    db.commit()
    db.refresh(db_product)
    
    return db_product


@router.put("/{product_id}", response_model=ProductSchema)
async def update_product(
    product_id: int,
    product_data: ProductUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """更新产品"""
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise not_found_exception("产品不存在")
    
    # 检查产品名是否与其他产品冲突
    if product_data.name and product_data.name != product.name:
        existing_product = db.query(Product).filter(Product.name == product_data.name).first()
        if existing_product:
            raise bad_request_exception("产品名已存在")
    
    # 更新产品信息
    update_data = product_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(product, field, value)
    
    db.commit()
    db.refresh(product)
    
    return product


@router.delete("/{product_id}")
async def delete_product(
    product_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """删除产品"""
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise not_found_exception("产品不存在")
    
    db.delete(product)
    db.commit()
    
    return {"message": "产品删除成功"}


@router.get("/search/", response_model=List[ProductSchema])
async def search_products(
    q: str = Query(..., min_length=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """搜索产品"""
    products = db.query(Product).filter(
        Product.name.contains(q) | Product.description.contains(q)
    ).all()
    
    return products
