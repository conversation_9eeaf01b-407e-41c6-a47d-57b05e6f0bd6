"""
报告生成API路由
"""

import os
import tempfile
from datetime import datetime
from typing import List, Dict
from fastapi import APIRouter, Depends, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
import pandas as pd

from app.database import get_db
from app.models.user import User
from app.models.measurement import Measurement
from app.core.security import get_current_active_user
from app.core.exceptions import not_found_exception, bad_request_exception
from app.utils.data_utils import (
    point_spc, generate_chart_base64, generate_report, save_excel_data
)
from app.config import settings

router = APIRouter()


@router.post("/generate")
async def generate_analysis_report(
    file_ids: List[int],
    points: List[str] = None,
    product_name: str = "Unknown",
    include_charts: bool = True,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """生成分析报告"""
    if not file_ids:
        raise bad_request_exception("文件ID列表不能为空")
    
    # 获取测量数据
    measurements = (
        db.query(Measurement)
        .filter(Measurement.file_id.in_(file_ids))
        .all()
    )
    
    if not measurements:
        raise not_found_exception("未找到测量数据")
    
    # 转换为DataFrame
    data = []
    for m in measurements:
        data.append({
            'point': m.point,
            'deviation': m.deviation,
            'up_tol': m.up_tol,
            'low_tol': m.low_tol,
            'snplus': m.snplus,
            'sn': m.sn,
            'nominal': m.nominal,
            'measurement': m.measurement,
            'dimension': m.dimension,
            'feature': m.feature,
            'axis': m.axis,
            'folder': m.folder,
            'oot': m.oot or 0
        })
    
    df = pd.DataFrame(data)
    
    # 如果没有指定测量点，使用所有测量点
    if not points:
        points = sorted(df['point'].drop_duplicates().tolist())
    
    # 执行SPC分析
    df_spc = point_spc(df, points)
    
    # 生成图表
    charts_base64 = {}
    if include_charts:
        for point in points:
            try:
                chart_base64 = generate_chart_base64(df, point)
                charts_base64[point] = chart_base64
            except Exception as e:
                print(f"生成图表失败 {point}: {e}")
    
    # 生成时间戳
    time_now = datetime.now().strftime("%Y%m%d%H%M%S")
    
    # 生成Word报告
    try:
        report_path = generate_report(df_spc, points, charts_base64, product_name, time_now)
        
        # 生成Excel数据文件
        excel_path = save_excel_data(df, df_spc, product_name, time_now)
        
        return {
            "message": "报告生成成功",
            "report_files": {
                "word_report": f"/api/reports/download/word/{os.path.basename(report_path)}",
                "excel_data": f"/api/reports/download/excel/{os.path.basename(excel_path)}"
            },
            "analysis_summary": {
                "total_points": len(points),
                "total_measurements": len(df),
                "files_processed": len(file_ids)
            }
        }
        
    except Exception as e:
        raise bad_request_exception(f"报告生成失败: {str(e)}")


@router.get("/download/word/{filename}")
async def download_word_report(
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """下载Word报告"""
    file_path = os.path.join(tempfile.gettempdir(), filename)
    
    if not os.path.exists(file_path):
        raise not_found_exception("报告文件不存在")
    
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    )


@router.get("/download/excel/{filename}")
async def download_excel_report(
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """下载Excel数据文件"""
    file_path = os.path.join(tempfile.gettempdir(), filename)
    
    if not os.path.exists(file_path):
        raise not_found_exception("数据文件不存在")
    
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


@router.get("/")
async def list_reports(
    current_user: User = Depends(get_current_active_user)
):
    """列出可用的报告文件"""
    temp_dir = tempfile.gettempdir()
    reports = []
    
    try:
        for filename in os.listdir(temp_dir):
            if filename.endswith(('.docx', '.xlsx')):
                file_path = os.path.join(temp_dir, filename)
                file_stat = os.stat(file_path)
                
                reports.append({
                    "filename": filename,
                    "size": file_stat.st_size,
                    "created_time": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                    "type": "word" if filename.endswith('.docx') else "excel",
                    "download_url": f"/api/reports/download/{'word' if filename.endswith('.docx') else 'excel'}/{filename}"
                })
    except Exception as e:
        print(f"列出报告文件失败: {e}")
    
    return {"reports": reports, "total": len(reports)}


@router.delete("/{filename}")
async def delete_report(
    filename: str,
    current_user: User = Depends(get_current_active_user)
):
    """删除报告文件"""
    file_path = os.path.join(tempfile.gettempdir(), filename)
    
    if not os.path.exists(file_path):
        raise not_found_exception("报告文件不存在")
    
    try:
        os.remove(file_path)
        return {"message": "报告文件删除成功"}
    except Exception as e:
        raise bad_request_exception(f"删除文件失败: {str(e)}")
