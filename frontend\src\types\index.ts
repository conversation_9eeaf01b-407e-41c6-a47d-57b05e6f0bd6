// 用户相关类型
export interface User {
  id: number
  username: string
  is_active: boolean
  created_at: string
  updated_at?: string
}

export interface LoginForm {
  username: string
  password: string
}

export interface Token {
  access_token: string
  token_type: string
}

// 产品相关类型
export interface Product {
  id: number
  name: string
  path: string
  description?: string
  created_at: string
  updated_at?: string
}

export interface ProductForm {
  name: string
  path: string
  description?: string
}

// 文件相关类型
export enum FileStatus {
  UPLOADED = 'uploaded',
  PROCESSING = 'processing',
  PROCESSED = 'processed',
  ERROR = 'error'
}

export interface FileInfo {
  id: number
  filename: string
  filepath: string
  file_size?: number
  product_id: number
  status: FileStatus
  upload_time: string
  processed_time?: string
}

export interface FileProcessResult {
  file_id: number
  status: FileStatus
  message: string
  measurement_count?: number
  error_details?: string
}

// 测量数据相关类型
export interface Measurement {
  id: number
  file_id: number
  dimension?: string
  feature?: string
  axis?: string
  nominal?: number
  up_tol?: number
  low_tol?: number
  measurement?: number
  deviation?: number
  point?: string
  folder?: string
  sn?: string
  snplus?: number
  oot?: number
  created_at: string
}

export interface MeasurementData {
  measurements: Measurement[]
  total: number
  points: string[]
}

// SPC分析相关类型
export interface SPCResult {
  point: string
  max_value: number
  min_value: number
  mean: number
  std: number
  ca: number
  cp: number
  cpk: number
  sample_count: number
}

export interface SPCAnalysis {
  results: SPCResult[]
  total_points: number
  analysis_time: string
}

// 帕累托分析相关类型
export interface ParetoData {
  point: string
  non_zero_count: number
  percentage: number
  cumulative_percentage: number
}

export interface ParetoAnalysis {
  data: ParetoData[]
  total_count: number
  analysis_time: string
}

// API响应类型
export interface ApiResponse<T = any> {
  data?: T
  message?: string
  error?: string
}

export interface ListResponse<T> {
  items: T[]
  total: number
}

// 图表相关类型
export interface ChartData {
  charts: Record<string, string>
}

// 报告相关类型
export interface ReportFile {
  filename: string
  size: number
  created_time: string
  type: 'word' | 'excel'
  download_url: string
}

export interface ReportGeneration {
  message: string
  report_files: {
    word_report: string
    excel_data: string
  }
  analysis_summary: {
    total_points: number
    total_measurements: number
    files_processed: number
  }
}
