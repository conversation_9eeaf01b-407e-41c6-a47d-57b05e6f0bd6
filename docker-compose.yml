version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./pystation.db
      - SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/reports:/app/reports
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  frontend:
    build: ./frontend
    ports:
      - "5173:5173"
    depends_on:
      - backend
    command: npm run dev -- --host 0.0.0.0

  # 可选：PostgreSQL数据库
  # db:
  #   image: postgres:15
  #   environment:
  #     POSTGRES_DB: pystation
  #     POSTGRES_USER: pystation
  #     POSTGRES_PASSWORD: password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"

# volumes:
#   postgres_data:
