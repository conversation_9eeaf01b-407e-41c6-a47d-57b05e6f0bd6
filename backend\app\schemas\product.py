"""
产品相关的Pydantic模式
"""

from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List


class ProductBase(BaseModel):
    """产品基础模式"""
    name: str
    path: str
    description: Optional[str] = None


class ProductCreate(ProductBase):
    """创建产品模式"""
    pass


class ProductUpdate(BaseModel):
    """更新产品模式"""
    name: Optional[str] = None
    path: Optional[str] = None
    description: Optional[str] = None


class Product(ProductBase):
    """产品响应模式"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class ProductList(BaseModel):
    """产品列表响应模式"""
    products: List[Product]
    total: int
