"""
测量数据相关的Pydantic模式
"""

from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List, Dict, Any


class MeasurementBase(BaseModel):
    """测量数据基础模式"""
    dimension: Optional[str] = None
    feature: Optional[str] = None
    axis: Optional[str] = None
    nominal: Optional[float] = None
    up_tol: Optional[float] = None
    low_tol: Optional[float] = None
    measurement: Optional[float] = None
    deviation: Optional[float] = None
    point: Optional[str] = None
    folder: Optional[str] = None
    sn: Optional[str] = None
    snplus: Optional[int] = None
    oot: Optional[int] = 0


class Measurement(MeasurementBase):
    """测量数据响应模式"""
    id: int
    file_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class MeasurementData(BaseModel):
    """测量数据集合模式"""
    measurements: List[Measurement]
    total: int
    points: List[str]  # 测量点列表


class SPCResult(BaseModel):
    """SPC分析结果模式"""
    point: str
    max_value: float
    min_value: float
    mean: float
    std: float
    ca: float
    cp: float
    cpk: float
    sample_count: int


class SPCAnalysis(BaseModel):
    """SPC分析响应模式"""
    results: List[SPCResult]
    total_points: int
    analysis_time: datetime


class ParetoData(BaseModel):
    """帕累托分析数据模式"""
    point: str
    non_zero_count: int
    percentage: float
    cumulative_percentage: float


class ParetoAnalysis(BaseModel):
    """帕累托分析响应模式"""
    data: List[ParetoData]
    total_count: int
    analysis_time: datetime
