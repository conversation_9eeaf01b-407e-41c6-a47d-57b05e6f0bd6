import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, Token } from '@/types'
import { authApi } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)

  // 方法
  const login = async (loginForm: LoginForm): Promise<boolean> => {
    try {
      loading.value = true
      const response = await authApi.login(loginForm)
      
      if (response.access_token) {
        token.value = response.access_token
        localStorage.setItem('token', response.access_token)
        
        // 获取用户信息
        await getCurrentUser()
        return true
      }
      return false
    } catch (error) {
      console.error('Login failed:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      token.value = null
      user.value = null
      localStorage.removeItem('token')
    }
  }

  const getCurrentUser = async () => {
    try {
      if (!token.value) return
      
      const userData = await authApi.getCurrentUser()
      user.value = userData
    } catch (error) {
      console.error('Get current user failed:', error)
      // 如果获取用户信息失败，可能是token过期，清除登录状态
      await logout()
    }
  }

  const register = async (registerForm: LoginForm): Promise<boolean> => {
    try {
      loading.value = true
      await authApi.register(registerForm)
      return true
    } catch (error) {
      console.error('Register failed:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 初始化时检查token有效性
  const initAuth = async () => {
    if (token.value) {
      await getCurrentUser()
    }
  }

  return {
    // 状态
    token,
    user,
    loading,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    logout,
    getCurrentUser,
    register,
    initAuth
  }
})
