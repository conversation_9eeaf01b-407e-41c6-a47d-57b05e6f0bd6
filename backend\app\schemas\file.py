"""
文件相关的Pydantic模式
"""

from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List
from enum import Enum


class FileStatus(str, Enum):
    """文件状态枚举"""
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    ERROR = "error"


class FileBase(BaseModel):
    """文件基础模式"""
    filename: str
    file_size: Optional[int] = None


class FileUpload(BaseModel):
    """文件上传模式"""
    product_id: int


class File(FileBase):
    """文件响应模式"""
    id: int
    filepath: str
    product_id: int
    status: FileStatus
    upload_time: datetime
    processed_time: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class FileList(BaseModel):
    """文件列表响应模式"""
    files: List[File]
    total: int


class FileProcessResult(BaseModel):
    """文件处理结果模式"""
    file_id: int
    status: FileStatus
    message: str
    measurement_count: Optional[int] = None
    error_details: Optional[str] = None
