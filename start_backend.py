#!/usr/bin/env python3
"""
PyStation Web Backend 启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """启动后端服务"""
    # 获取项目根目录
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    # 检查后端目录是否存在
    if not backend_dir.exists():
        print("❌ 后端目录不存在")
        return 1
    
    # 切换到后端目录
    os.chdir(backend_dir)
    
    # 检查是否安装了依赖
    try:
        import fastapi
        import uvicorn
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请先安装依赖: pip install -r requirements.txt")
        return 1
    
    # 创建必要的目录
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("reports", exist_ok=True)
    print("✅ 目录创建完成")
    
    # 启动服务
    print("🚀 启动FastAPI服务...")
    print("📍 API地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔄 按 Ctrl+C 停止服务")
    
    try:
        # 使用uvicorn启动
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
