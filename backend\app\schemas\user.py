"""
用户相关的Pydantic模式
"""

from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class UserBase(BaseModel):
    """用户基础模式"""
    username: str


class UserCreate(UserBase):
    """创建用户模式"""
    password: str


class UserLogin(BaseModel):
    """用户登录模式"""
    username: str
    password: str


class User(UserBase):
    """用户响应模式"""
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class Token(BaseModel):
    """JWT令牌模式"""
    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    """令牌数据模式"""
    username: Optional[str] = None
