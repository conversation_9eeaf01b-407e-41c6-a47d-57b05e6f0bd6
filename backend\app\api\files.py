"""
文件处理API路由
"""

import os
import shutil
from typing import List
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, status
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.user import User
from app.models.product import Product
from app.models.file import File as FileModel
from app.schemas.file import File as FileSchema, FileList, FileProcessResult, FileStatus
from app.core.security import get_current_active_user
from app.core.exceptions import not_found_exception, bad_request_exception
from app.config import settings
from app.utils.data_utils import process_tmp_files, read_tmp
from app.models.measurement import Measurement

router = APIRouter()


@router.post("/upload/{product_id}", response_model=FileSchema)
async def upload_file(
    product_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """上传TMP文件"""
    # 验证产品存在
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise not_found_exception("产品不存在")
    
    # 验证文件类型
    if not file.filename.lower().endswith(('.tmp', '.TMP')):
        raise bad_request_exception("只支持TMP文件格式")
    
    # 验证文件大小
    if file.size and file.size > settings.MAX_FILE_SIZE:
        raise bad_request_exception(f"文件大小超过限制 ({settings.MAX_FILE_SIZE} bytes)")
    
    # 创建上传目录
    upload_dir = os.path.join(settings.UPLOAD_DIR, str(product_id))
    os.makedirs(upload_dir, exist_ok=True)
    
    # 保存文件
    file_path = os.path.join(upload_dir, file.filename)
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    # 创建文件记录
    db_file = FileModel(
        filename=file.filename,
        filepath=file_path,
        file_size=file.size,
        product_id=product_id,
        status=FileStatus.UPLOADED
    )
    db.add(db_file)
    db.commit()
    db.refresh(db_file)
    
    return db_file


@router.get("/", response_model=FileList)
async def get_files(
    product_id: int = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取文件列表"""
    query = db.query(FileModel)
    
    if product_id:
        query = query.filter(FileModel.product_id == product_id)
    
    files = query.offset(skip).limit(limit).all()
    total = query.count()
    
    return FileList(files=files, total=total)


@router.get("/{file_id}", response_model=FileSchema)
async def get_file(
    file_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取单个文件信息"""
    file = db.query(FileModel).filter(FileModel.id == file_id).first()
    if not file:
        raise not_found_exception("文件不存在")
    
    return file


@router.delete("/{file_id}")
async def delete_file(
    file_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """删除文件"""
    file = db.query(FileModel).filter(FileModel.id == file_id).first()
    if not file:
        raise not_found_exception("文件不存在")
    
    # 删除物理文件
    if os.path.exists(file.filepath):
        os.remove(file.filepath)
    
    # 删除数据库记录
    db.delete(file)
    db.commit()
    
    return {"message": "文件删除成功"}


@router.post("/{file_id}/process", response_model=FileProcessResult)
async def process_file(
    file_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """处理TMP文件"""
    file = db.query(FileModel).filter(FileModel.id == file_id).first()
    if not file:
        raise not_found_exception("文件不存在")
    
    if file.status == FileStatus.PROCESSED:
        return FileProcessResult(
            file_id=file_id,
            status=FileStatus.PROCESSED,
            message="文件已处理过",
            measurement_count=db.query(Measurement).filter(Measurement.file_id == file_id).count()
        )
    
    try:
        # 更新状态为处理中
        file.status = FileStatus.PROCESSING
        db.commit()
        
        # 处理文件
        file_dir = os.path.dirname(file.filepath)
        oktmp_list, noktmp_list, duplicate_names = process_tmp_files(file_dir)
        
        if file.filepath not in oktmp_list:
            file.status = FileStatus.ERROR
            db.commit()
            return FileProcessResult(
                file_id=file_id,
                status=FileStatus.ERROR,
                message="文件处理失败：文件格式不正确或内容有误",
                error_details="文件不在有效文件列表中"
            )
        
        # 读取数据
        df_read, ms_points = read_tmp([file.filepath])
        
        # 保存测量数据到数据库
        measurement_count = 0
        for _, row in df_read.iterrows():
            measurement = Measurement(
                file_id=file_id,
                dimension=row.get('dimension'),
                feature=row.get('feature'),
                axis=row.get('axis'),
                nominal=row.get('nominal'),
                up_tol=row.get('up_tol'),
                low_tol=row.get('low_tol'),
                measurement=row.get('measurement'),
                deviation=row.get('deviation'),
                point=row.get('point'),
                folder=row.get('folder'),
                sn=row.get('sn'),
                snplus=row.get('snplus'),
                oot=row.get('oot')
            )
            db.add(measurement)
            measurement_count += 1
        
        # 更新文件状态
        file.status = FileStatus.PROCESSED
        file.processed_time = db.func.now()
        db.commit()
        
        return FileProcessResult(
            file_id=file_id,
            status=FileStatus.PROCESSED,
            message="文件处理成功",
            measurement_count=measurement_count
        )
        
    except Exception as e:
        file.status = FileStatus.ERROR
        db.commit()
        return FileProcessResult(
            file_id=file_id,
            status=FileStatus.ERROR,
            message="文件处理失败",
            error_details=str(e)
        )


@router.get("/{file_id}/download")
async def download_file(
    file_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """下载文件"""
    file = db.query(FileModel).filter(FileModel.id == file_id).first()
    if not file:
        raise not_found_exception("文件不存在")
    
    if not os.path.exists(file.filepath):
        raise not_found_exception("文件不存在于服务器")
    
    return FileResponse(
        path=file.filepath,
        filename=file.filename,
        media_type='application/octet-stream'
    )
