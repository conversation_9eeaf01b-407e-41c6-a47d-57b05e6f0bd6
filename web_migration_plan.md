# PyStation Web迁移计划

## 项目概述
将现有的PyQt5桌面应用迁移到FastAPI + Vue的Web架构。

## 技术栈

### 后端 (FastAPI)
- **框架**: FastAPI 0.104+
- **数据库**: SQLAlchemy + SQLite/PostgreSQL
- **数据处理**: Pandas, NumPy, SciPy
- **可视化**: Matplotlib (后端生成)
- **文档处理**: python-docx, openpyxl
- **认证**: JWT Token
- **文件处理**: aiofiles

### 前端 (Vue 3)
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI库**: Element Plus
- **图表库**: ECharts
- **HTTP客户端**: Axios
- **状态管理**: Pinia

## 项目结构

```
pystation-web/
├── backend/                    # FastAPI后端
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI应用入口
│   │   ├── config.py          # 配置管理
│   │   ├── database.py        # 数据库配置
│   │   ├── models/            # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── product.py
│   │   │   └── measurement.py
│   │   ├── schemas/           # Pydantic模式
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── product.py
│   │   │   └── measurement.py
│   │   ├── api/               # API路由
│   │   │   ├── __init__.py
│   │   │   ├── auth.py        # 认证相关
│   │   │   ├── products.py    # 产品管理
│   │   │   ├── files.py       # 文件处理
│   │   │   ├── analysis.py    # 数据分析
│   │   │   └── reports.py     # 报告生成
│   │   ├── services/          # 业务逻辑
│   │   │   ├── __init__.py
│   │   │   ├── auth_service.py
│   │   │   ├── data_service.py    # 数据处理服务
│   │   │   ├── file_service.py    # 文件处理服务
│   │   │   ├── chart_service.py   # 图表生成服务
│   │   │   └── report_service.py  # 报告生成服务
│   │   ├── utils/             # 工具函数
│   │   │   ├── __init__.py
│   │   │   ├── data_utils.py  # 迁移现有数据处理逻辑
│   │   │   ├── file_utils.py
│   │   │   └── chart_utils.py
│   │   └── core/              # 核心功能
│   │       ├── __init__.py
│   │       ├── security.py    # 安全相关
│   │       └── exceptions.py  # 异常处理
│   ├── uploads/               # 上传文件存储
│   ├── reports/               # 生成的报告
│   ├── requirements.txt       # Python依赖
│   └── alembic/              # 数据库迁移
├── frontend/                  # Vue前端
│   ├── public/
│   ├── src/
│   │   ├── main.ts           # 应用入口
│   │   ├── App.vue           # 根组件
│   │   ├── components/       # 组件
│   │   │   ├── common/       # 通用组件
│   │   │   ├── charts/       # 图表组件
│   │   │   ├── upload/       # 文件上传组件
│   │   │   └── tables/       # 表格组件
│   │   ├── views/            # 页面视图
│   │   │   ├── Login.vue     # 登录页面
│   │   │   ├── Dashboard.vue # 主面板
│   │   │   ├── Products.vue  # 产品管理
│   │   │   ├── Analysis.vue  # 数据分析
│   │   │   └── Reports.vue   # 报告管理
│   │   ├── router/           # 路由配置
│   │   ├── stores/           # 状态管理
│   │   ├── api/              # API接口
│   │   ├── types/            # TypeScript类型
│   │   └── utils/            # 工具函数
│   ├── package.json
│   ├── vite.config.ts
│   └── tsconfig.json
├── docker-compose.yml         # Docker部署配置
├── README.md
└── .gitignore
```

## API接口设计

### 认证相关
- POST /api/auth/login - 用户登录
- POST /api/auth/logout - 用户登出
- GET /api/auth/me - 获取当前用户信息

### 产品管理
- GET /api/products - 获取产品列表
- POST /api/products - 添加产品
- PUT /api/products/{id} - 更新产品
- DELETE /api/products/{id} - 删除产品

### 文件处理
- POST /api/files/upload - 上传TMP文件
- GET /api/files/{id} - 获取文件信息
- DELETE /api/files/{id} - 删除文件

### 数据分析
- POST /api/analysis/process - 处理TMP文件数据
- GET /api/analysis/points - 获取测量点列表
- POST /api/analysis/spc - 执行SPC分析
- POST /api/analysis/pareto - 生成帕累托分析

### 报告生成
- POST /api/reports/generate - 生成报告
- GET /api/reports/{id} - 下载报告
- GET /api/reports - 获取报告列表

## 数据库设计

### 用户表 (users)
- id: 主键
- username: 用户名
- password_hash: 密码哈希
- created_at: 创建时间
- updated_at: 更新时间

### 产品表 (products)
- id: 主键
- name: 产品名称
- path: 产品路径
- created_at: 创建时间
- updated_at: 更新时间

### 文件表 (files)
- id: 主键
- filename: 文件名
- filepath: 文件路径
- product_id: 产品ID (外键)
- upload_time: 上传时间
- file_size: 文件大小
- status: 处理状态

### 测量数据表 (measurements)
- id: 主键
- file_id: 文件ID (外键)
- dimension: 尺寸
- feature: 特征
- axis: 轴向
- nominal: 标称值
- up_tol: 上公差
- low_tol: 下公差
- measurement: 测量值
- deviation: 偏差
- point: 测量点
- sn: 序列号

## 迁移步骤

1. **项目架构设计和规划** ✓
2. **FastAPI后端开发**
   - 设置项目结构
   - 实现认证系统
   - 迁移数据处理逻辑
   - 实现API接口
3. **Vue前端开发**
   - 设置项目结构
   - 实现用户界面
   - 集成图表库
   - 实现文件上传
4. **数据处理模块迁移**
   - 迁移data_utils.py逻辑
   - 适配Web环境
   - 优化性能
5. **可视化功能实现**
   - 使用ECharts替代matplotlib
   - 实现交互式图表
6. **文件管理和报告生成**
   - 实现文件上传处理
   - 迁移报告生成逻辑
7. **用户认证和配置管理**
   - JWT认证系统
   - 产品配置管理
8. **测试和部署**
   - 单元测试
   - 集成测试
   - Docker部署配置

## 关键技术点

1. **异步处理**: 使用FastAPI的异步特性处理大文件和长时间运算
2. **文件上传**: 支持大文件分片上传
3. **数据可视化**: 前端实时渲染图表，后端提供数据
4. **缓存策略**: Redis缓存分析结果
5. **错误处理**: 统一的错误处理和日志记录
6. **安全性**: JWT认证、文件类型验证、SQL注入防护

## 预期收益

1. **跨平台访问**: 通过浏览器在任何设备上使用
2. **并发处理**: 支持多用户同时使用
3. **易于部署**: Docker容器化部署
4. **可扩展性**: 微服务架构，易于扩展
5. **现代化UI**: 响应式设计，更好的用户体验
