"""
产品路径管理对话框模块
提供产品路径的增删改查功能
"""

# ============================================================================
# 标准库导入
# ============================================================================
import json
import os
import re

# ============================================================================
# 第三方库导入
# ============================================================================
from PyQt5 import QtWidgets
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QTableWidget, QTableWidgetItem,
    QHBoxLayout, QLineEdit, QPushButton, QLabel, QMessageBox
)

class ProductPathDialog(QDialog):
    def __init__(self, json_path=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle('Product Path Management')
        self.resize(1000, 350)
        
        # 如果没有提供路径，使用默认的绝对路径
        if json_path is None:
            # 获取当前文件所在目录的上级目录，然后拼接configs路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)  # 上级目录
            json_path = os.path.join(project_root, 'configs', 'products.json')
        
        self.json_path = json_path
        self.products = {}  # 改为字典格式
        self.init_ui()
        self.load_json()
        # 确保加载 JSON 后刷新表格显示数据
        self.refresh_table()

    def init_ui(self):
        layout = QVBoxLayout(self)
        self.table = QTableWidget(0, 2)
        self.table.setHorizontalHeaderLabels(['Product Name', 'Path'])

        # Get the horizontal header object
        header = self.table.horizontalHeader()
        # Set the 'Product Name' column to automatically adjust to the content width
        header.setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeToContents)
        # Set the 'Path' column to fill the remaining space
        header.setSectionResizeMode(1, QtWidgets.QHeaderView.Stretch)

        layout.addWidget(self.table)

        add_layout = QHBoxLayout()
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText('Product Name')
        self.path_input = QLineEdit()
        self.path_input.setPlaceholderText('Path')
        add_btn = QPushButton('Add')
        add_btn.clicked.connect(self.add_product)
        add_layout.addWidget(QLabel('Name:'))
        add_layout.addWidget(self.name_input)
        add_layout.addWidget(QLabel('Path:'))
        add_layout.addWidget(self.path_input)
        add_layout.addWidget(add_btn)
        layout.addLayout(add_layout)

        btn_layout = QHBoxLayout()
        del_btn = QPushButton('Delete Selected')
        del_btn.clicked.connect(self.delete_selected)
        save_btn = QPushButton('Save')
        save_btn.clicked.connect(self.save_json)
        btn_layout.addWidget(del_btn)
        btn_layout.addWidget(save_btn)
        layout.addLayout(btn_layout)

    def load_json(self):
        try:
            with open(self.json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 处理不同的数据格式
                if isinstance(data, dict):
                    # 新格式：{name: path}
                    self.products = data
                elif isinstance(data, list):
                    # 旧格式：[{name: "", path: ""}] 转换为新格式
                    self.products = {item.get('name', ''): item.get('path', '') for item in data if isinstance(item, dict)}
                else:
                    self.products = {}
        except Exception:
            self.products = {}

    def save_json(self):
        try:
            with open(self.json_path, 'w', encoding='utf-8') as f:
                json.dump(self.products, f, ensure_ascii=False, indent=2)
            QMessageBox.information(self, 'Prompt', 'Saved successfully')
        except Exception as e:
            QMessageBox.warning(self, 'Error', f'Save failed: {e}')

    def refresh_table(self):
        self.table.setRowCount(0)
        for name, path in self.products.items():
            row = self.table.rowCount()
            self.table.insertRow(row)
            self.table.setItem(row, 0, QTableWidgetItem(name))
            self.table.setItem(row, 1, QTableWidgetItem(path))

    def add_product(self):
        name = self.name_input.text().strip()
        path = self.path_input.text().strip()
        if not name or not path:
            QMessageBox.warning(self, 'Prompt', 'Name and path cannot be empty')
            return
        if not re.fullmatch(r'\w+', name):
            QMessageBox.warning(self, 'Prompt', 'Product name can only contain letters, numbers, and underscores')
            return
        if not os.path.isabs(path):
            QMessageBox.warning(self, 'Prompt', 'Path must be an absolute path, e.g., D:\\folder or /home/<USER>')
            return
        # 检查重复的产品名称
        if name in self.products:
            QMessageBox.warning(self, 'Prompt', f'Product name "{name}" already exists')
            return
        # 检查重复的路径
        if path in self.products.values():
            QMessageBox.warning(self, 'Prompt', f'Path "{path}" already exists')
            return
        self.products[name] = path
        self.refresh_table()
        self.name_input.clear()
        self.path_input.clear()

    def delete_selected(self):
        rows = sorted(set(idx.row() for idx in self.table.selectedIndexes()), reverse=True)
        product_names = list(self.products.keys())
        for row in rows:
            if 0 <= row < len(product_names):
                name_to_delete = product_names[row]
                del self.products[name_to_delete]
        self.refresh_table()
