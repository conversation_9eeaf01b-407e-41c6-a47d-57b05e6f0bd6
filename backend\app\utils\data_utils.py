"""
数据处理工具模块 - 从PyQt5应用迁移
提供TMP文件处理、数据分析、报告生成等功能
"""

import os
import tempfile
from typing import List, Tuple, Dict, Any, Optional
from pathlib import Path
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_agg import FigureCanvasAgg
from docx import Document
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT, WD_TABLE_ALIGNMENT
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.shared import Inches, Pt
import io
import base64

from app.core.exceptions import DataProcessingError, FileProcessingError


def process_tmp_files(folder_path: str) -> Tuple[List[str], List[str], List[str]]:
    """
    处理TMP文件，分类为有效文件、无效文件和重复文件
    
    Args:
        folder_path: 文件夹路径
        
    Returns:
        Tuple[有效文件列表, 无效文件列表, 重复文件名列表]
    """
    if not os.path.exists(folder_path):
        raise FileProcessingError(f"文件夹不存在: {folder_path}")
    
    tmp_files = []
    # 遍历当前文件夹下的所有文件
    for file in os.listdir(folder_path):
        file_path = os.path.join(folder_path, file)
        # 仅处理文件且扩展名是.tmp的情况
        if os.path.isfile(file_path) and file.lower().endswith('.tmp'):
            tmp_files.append(file_path)
    
    oktmp_list = []
    noktmp_list = []
    name_to_paths = {}
    
    # 按文件名分组，检测重复
    for file_path in tmp_files:
        name = os.path.splitext(os.path.basename(file_path))[0]
        if name not in name_to_paths:
            name_to_paths[name] = [file_path]
        else:
            name_to_paths[name].append(file_path)
    
    duplicate_names = [name for name, paths in name_to_paths.items() if len(paths) > 1]
    unique_files = [paths[0] for name, paths in name_to_paths.items() if len(paths) == 1]
    
    # 验证文件内容
    for file_path in unique_files:
        try:
            with open(file_path, 'r', encoding='Windows-1252') as f:
                file_content = f.read()
            file_line_count = file_content.count("FILE:")
            if file_line_count == 1:
                oktmp_list.append(file_path)
            else:
                noktmp_list.append(file_path)
        except Exception:
            noktmp_list.append(file_path)
    
    return oktmp_list, noktmp_list, duplicate_names


def create_oot(row: pd.Series) -> float:
    """
    计算超差值 (Out of Tolerance)
    
    Args:
        row: 包含deviation, low_tol, up_tol的数据行
        
    Returns:
        超差值，0表示在公差范围内
    """
    if row['low_tol'] <= row['deviation'] <= row['up_tol']:
        return 0
    elif row['deviation'] < row['low_tol']:
        return row['deviation'] - row['low_tol']
    elif row['deviation'] > row['up_tol']:
        return row['deviation'] - row['up_tol']
    else:
        return 0


def read_tmp(tmp_list: List[str]) -> Tuple[pd.DataFrame, List[str]]:
    """
    读取TMP文件并解析数据
    
    Args:
        tmp_list: TMP文件路径列表
        
    Returns:
        Tuple[解析后的数据框, 测量点列表]
    """
    if not tmp_list:
        raise DataProcessingError("TMP文件列表为空")
    
    df_read = pd.DataFrame()
    i = 1
    
    for oktmp in tmp_list:
        try:
            with open(oktmp, 'r', encoding='Windows-1252') as file:
                folder_name = oktmp.split(os.path.sep)[-2]
                file_name = oktmp.split(os.path.sep)[-1]
                file_name, file_extension = os.path.splitext(file_name)
                lines = file.readlines()
                
                # 提取DIM2行
                dim2_lines = [line.strip() for line in lines if line.startswith('DIM2:')]
                if not dim2_lines:
                    continue
                
                df = pd.DataFrame(dim2_lines, columns=['A'])
                df[['B', 'feature', 'C']] = df['A'].str.split('\x03', expand=True)
                df = df.drop('A', axis=1)
                df[['D', 'dimension']] = df['B'].str.split(': ', expand=True)
                df = df.drop(['B', 'D'], axis=1)
                df[['axis', 'nominal', 'up_tol', 'low_tol_abs', 'measurement']] = df['C'].str.split('\s+', expand=True)
                df = df.drop('C', axis=1)
                
                # 重新排列列顺序
                df = df[['dimension', 'feature', 'axis', 'nominal', 'up_tol', 'low_tol_abs', 'measurement']]
                
                # 数据类型转换
                df['dimension'] = df['dimension'].astype(str)
                df['feature'] = df['feature'].astype(str)
                df['axis'] = df['axis'].astype(str)
                df['nominal'] = df['nominal'].astype(float).round(3)
                df['up_tol'] = df['up_tol'].astype(float)
                df['low_tol_abs'] = df['low_tol_abs'].astype(float)
                df['measurement'] = df['measurement'].astype(float).round(3)
                
                # 计算衍生字段
                df['low_tol'] = df['low_tol_abs'] * -1
                df['deviation'] = (df['measurement'] - df['nominal']).round(3)
                df['point'] = (df['dimension'].astype(str) + '.' + df['axis'] + 
                              '-nominal-' + df['nominal'].astype(str) + 
                              '-feature-' + df['feature'].astype(str))
                df['folder'] = folder_name
                df['sn'] = file_name
                df['snplus'] = i
                df['oot'] = df.apply(create_oot, axis=1)
                
                df_read = pd.concat([df_read, df], ignore_index=True)
                i += 1
                
        except Exception as e:
            raise DataProcessingError(f"读取文件 {oktmp} 失败: {str(e)}")
    
    if df_read.empty:
        raise DataProcessingError("没有成功读取任何数据")
    
    ms_points = sorted(df_read['point'].drop_duplicates().tolist())
    return df_read, ms_points


def point_spc(df: pd.DataFrame, points: List[str]) -> pd.DataFrame:
    """
    计算SPC统计数据

    Args:
        df: 测量数据框
        points: 测量点列表

    Returns:
        SPC统计结果数据框
    """
    if df.empty:
        raise DataProcessingError("数据框为空")

    if not points:
        raise DataProcessingError("测量点列表为空")

    df_spc = pd.DataFrame()

    for point in points:
        df_filter = df[df['point'] == point]
        if df_filter.empty:
            continue

        df_filter = df_filter.sort_values(by=['snplus'])

        # 获取公差值
        dv_usl = df_filter['up_tol'].drop_duplicates().values.tolist()[0]
        dv_lsl = df_filter['low_tol'].drop_duplicates().values.tolist()[0]

        # 计算统计值
        dv_max = df_filter['deviation'].max()
        dv_min = df_filter['deviation'].min()
        dv_c = (dv_usl + dv_lsl) / 2
        dv_t = dv_usl - dv_lsl
        dv_mean = df_filter['deviation'].mean()
        dv_std = df_filter['deviation'].std()

        # 计算SPC指标
        dv_ca = (dv_mean - dv_c) / (dv_t / 2) if dv_t != 0 else 0
        dv_cp = dv_t / (6 * dv_std) if dv_std != 0 else 0
        dv_cpk = dv_cp * (1 - abs(dv_ca)) if dv_cp != 0 else 0

        data = {
            'point': [point],
            'max': [round(dv_max, 2)],
            'min': [round(dv_min, 2)],
            'mean': [round(dv_mean, 2)],
            'std': [round(dv_std, 2)],
            'ca': [round(dv_ca, 2)],
            'cp': [round(dv_cp, 2)],
            'cpk': [round(dv_cpk, 2)],
            'nominal_quantity': [len(df_filter['nominal'].drop_duplicates().values.tolist())],
            'up_tol_quantity': [len(df_filter['up_tol'].drop_duplicates().values.tolist())],
            'low_tol_quantity': [len(df_filter['low_tol'].drop_duplicates().values.tolist())]
        }

        df_statistics = pd.DataFrame(data)
        df_spc = pd.concat([df_spc, df_statistics], ignore_index=True)

    return df_spc


def generate_chart_base64(df: pd.DataFrame, point: str) -> str:
    """
    生成单个测量点的图表并返回base64编码

    Args:
        df: 测量数据框
        point: 测量点名称

    Returns:
        图表的base64编码字符串
    """
    df_filter = df[df['point'] == point]
    if df_filter.empty:
        raise DataProcessingError(f"测量点 {point} 没有数据")

    df_filter = df_filter.sort_values(by=['snplus']).reset_index(drop=True)
    sn_quantity = len(df_filter['sn'].drop_duplicates().values.tolist())

    # 创建图表
    fig, ax = plt.subplots()
    fig.set_size_inches(5.6, 1.7)
    ax.set_xlabel('snplus')
    ax.set_ylabel('deviation')
    ax.tick_params(axis='both', labelsize=6)
    ax.set_xticks(np.arange(0, (sn_quantity + 1), 1))

    # 绘制公差线和中心线
    ax.plot(df_filter['snplus'], df_filter['up_tol'], color='red', linewidth=1, linestyle='--')
    ax.plot(df_filter['snplus'], df_filter['low_tol'], color='red', linewidth=1, linestyle='--')
    ax.plot(df_filter['snplus'], (df_filter['up_tol'] + df_filter['low_tol'])/2, color='black', linewidth=1)

    # 绘制测量数据
    ax.plot(df_filter['snplus'], df_filter['deviation'], color='blue', linewidth=1, marker='o', markersize=3)

    # 标注前5个和后5个数据点
    first_five_indices = df_filter.head(5).index
    for firstIndex in first_five_indices:
        ax.annotate(df_filter.loc[firstIndex, 'deviation'],
                    xy=(df_filter.loc[firstIndex, 'snplus'], df_filter.loc[firstIndex, 'deviation']),
                    xytext=(0, 5), textcoords='offset points', fontsize=7, rotation=90, color='green')

    last_five_indices = df_filter.tail(5).index
    for lastIndex in last_five_indices:
        ax.annotate(df_filter.loc[lastIndex, 'deviation'],
                    xy=(df_filter.loc[lastIndex, 'snplus'], df_filter.loc[lastIndex, 'deviation']),
                    xytext=(0, 5), textcoords='offset points', fontsize=7, rotation=90, color='purple')

    # 转换为base64
    buffer = io.BytesIO()
    fig.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.getvalue()).decode()
    plt.close(fig)

    return image_base64


def generate_report(df_spc: pd.DataFrame, points: List[str], charts_base64: Dict[str, str],
                   product: str, time_now: str) -> str:
    """
    生成Word报告

    Args:
        df_spc: SPC统计数据框
        points: 测量点列表
        charts_base64: 图表base64编码字典
        product: 产品名称
        time_now: 时间戳

    Returns:
        报告文件路径
    """
    document = Document()

    for point in points:
        # 添加测量点标题
        paragraph = document.add_paragraph(point)
        paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

        # 添加图表
        if point in charts_base64:
            paragraph = document.add_paragraph()
            paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

            # 将base64转换为图片并插入
            image_data = base64.b64decode(charts_base64[point])
            image_stream = io.BytesIO(image_data)
            run = paragraph.add_run("")
            run.add_picture(image_stream, width=Inches(5.86), height=Inches(1.78))

        # 添加统计表格
        table = document.add_table(rows=2, cols=7, style="Table Grid")
        spc_dict = df_spc[df_spc['point'] == point].to_dict('list')

        # 表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Max'
        hdr_cells[1].text = 'Min'
        hdr_cells[2].text = 'Mean'
        hdr_cells[3].text = 'Std'
        hdr_cells[4].text = 'Ca'
        hdr_cells[5].text = 'Cp'
        hdr_cells[6].text = 'Cpk'

        # 数据行
        hdr_cells_1 = table.rows[1].cells
        if spc_dict.get('max'):
            hdr_cells_1[0].text = str(spc_dict.get('max')[0])
            hdr_cells_1[1].text = str(spc_dict.get('min')[0])
            hdr_cells_1[2].text = str(spc_dict.get('mean')[0])
            hdr_cells_1[3].text = str(spc_dict.get('std')[0])
            hdr_cells_1[4].text = str(spc_dict.get('ca')[0])
            hdr_cells_1[5].text = str(spc_dict.get('cp')[0])
            hdr_cells_1[6].text = str(spc_dict.get('cpk')[0])

        document.add_paragraph()

    # 格式化表格
    for cpk_table in document.tables:
        cpk_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        for cpk_row in cpk_table.rows:
            for cpk_cell in cpk_row.cells:
                cpk_cell.vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER
                cpk_cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                paragraphs = cpk_cell.paragraphs
                for paragraph in paragraphs:
                    for run in paragraph.runs:
                        font = run.font
                        font.size = Pt(10)

    # 保存文档
    report_filename = f"{product}-{time_now}.docx"
    temp_dir = tempfile.gettempdir()
    save_path = os.path.join(temp_dir, report_filename)
    document.save(save_path)

    return save_path


def save_excel_data(df_raw: pd.DataFrame, df_spc: pd.DataFrame,
                   product: str, time_now: str) -> str:
    """
    保存Excel数据文件

    Args:
        df_raw: 原始测量数据
        df_spc: SPC统计数据
        product: 产品名称
        time_now: 时间戳

    Returns:
        Excel文件路径
    """
    # 创建数据透视表
    df_deviation = pd.pivot_table(
        df_raw,
        values='deviation',
        index=['point', 'dimension', 'feature', 'axis', 'nominal', 'up_tol', 'low_tol'],
        columns=['sn']
    ).reset_index()

    df_result = pd.merge(df_spc, df_deviation, on='point')

    # 超差数据透视表
    df_oot = pd.pivot_table(
        df_raw[df_raw['oot'] != 0],
        values='oot',
        index=['point'],
        columns=['sn']
    ).reset_index()

    # 保存Excel文件
    excel_filename = f"{product}-{time_now}.xlsx"
    temp_dir = tempfile.gettempdir()
    save_path = os.path.join(temp_dir, excel_filename)

    with pd.ExcelWriter(save_path) as writer:
        df_raw.to_excel(writer, sheet_name='database', index=False)
        df_result.to_excel(writer, sheet_name='deviation_data', index=False)
        df_oot.to_excel(writer, sheet_name='oot_pivot', index=False)

    return save_path


def calculate_pareto_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算帕累托分析数据

    Args:
        df: 测量数据框

    Returns:
        帕累托分析结果数据框
    """
    if df.empty:
        raise DataProcessingError("数据框为空")

    # 计算每个测量点的超差数量
    df_oot = df[df['oot'] != 0]
    pareto_data = df_oot.groupby('point').size().reset_index(name='non_zero_count')
    pareto_data = pareto_data.sort_values('non_zero_count', ascending=False)

    # 计算百分比和累计百分比
    total_count = pareto_data['non_zero_count'].sum()
    if total_count > 0:
        pareto_data['percentage'] = (pareto_data['non_zero_count'] / total_count * 100).round(2)
        pareto_data['cumulative_percentage'] = pareto_data['percentage'].cumsum().round(2)
    else:
        pareto_data['percentage'] = 0
        pareto_data['cumulative_percentage'] = 0

    return pareto_data
