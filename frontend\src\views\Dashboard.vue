<template>
  <div class="dashboard">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <div class="header-left">
        <h1>PyStation Web</h1>
      </div>
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-icon><User /></el-icon>
            {{ authStore.user?.username }}
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主体内容 -->
    <el-container class="main-container">
      <!-- 侧边栏 -->
      <el-aside width="200px" class="sidebar">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          router
        >
          <el-menu-item index="/">
            <el-icon><House /></el-icon>
            <span>主面板</span>
          </el-menu-item>
          <el-menu-item index="/products">
            <el-icon><Box /></el-icon>
            <span>产品管理</span>
          </el-menu-item>
          <el-menu-item index="/analysis">
            <el-icon><DataAnalysis /></el-icon>
            <span>数据分析</span>
          </el-menu-item>
          <el-menu-item index="/reports">
            <el-icon><Document /></el-icon>
            <span>报告管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="content">
        <div class="dashboard-content">
          <el-row :gutter="20">
            <!-- 统计卡片 -->
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-item">
                  <div class="stat-icon products">
                    <el-icon><Box /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ stats.products }}</div>
                    <div class="stat-label">产品数量</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-item">
                  <div class="stat-icon files">
                    <el-icon><Files /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ stats.files }}</div>
                    <div class="stat-label">文件数量</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-item">
                  <div class="stat-icon measurements">
                    <el-icon><DataLine /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ stats.measurements }}</div>
                    <div class="stat-label">测量数据</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-item">
                  <div class="stat-icon reports">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ stats.reports }}</div>
                    <div class="stat-label">报告数量</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 快速操作 -->
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>快速操作</span>
                </template>
                <div class="quick-actions">
                  <el-button type="primary" @click="$router.push('/products')">
                    <el-icon><Plus /></el-icon>
                    添加产品
                  </el-button>
                  <el-button type="success" @click="$router.push('/analysis')">
                    <el-icon><Upload /></el-icon>
                    上传文件
                  </el-button>
                  <el-button type="info" @click="$router.push('/analysis')">
                    <el-icon><DataAnalysis /></el-icon>
                    数据分析
                  </el-button>
                  <el-button type="warning" @click="$router.push('/reports')">
                    <el-icon><Document /></el-icon>
                    生成报告
                  </el-button>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>系统信息</span>
                </template>
                <div class="system-info">
                  <p><strong>版本:</strong> 1.0.0</p>
                  <p><strong>最后登录:</strong> {{ formatDate(authStore.user?.updated_at) }}</p>
                  <p><strong>系统状态:</strong> <el-tag type="success">正常</el-tag></p>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 统计数据
const stats = ref({
  products: 0,
  files: 0,
  measurements: 0,
  reports: 0
})

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  if (command === 'logout') {
    authStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  }
}

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString()
}

// 加载统计数据
const loadStats = async () => {
  // TODO: 实现统计数据加载
  stats.value = {
    products: 5,
    files: 23,
    measurements: 1250,
    reports: 8
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left h1 {
  color: #333;
  margin: 0;
}

.header-right .user-info {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.main-container {
  flex: 1;
}

.sidebar {
  background: #f5f5f5;
  border-right: 1px solid #e6e6e6;
}

.sidebar-menu {
  border: none;
  background: transparent;
}

.content {
  background: #f0f2f5;
  padding: 20px;
}

.dashboard-content {
  max-width: 1200px;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.products {
  background: #409eff;
}

.stat-icon.files {
  background: #67c23a;
}

.stat-icon.measurements {
  background: #e6a23c;
}

.stat-icon.reports {
  background: #f56c6c;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.system-info p {
  margin: 10px 0;
  color: #666;
}
</style>
