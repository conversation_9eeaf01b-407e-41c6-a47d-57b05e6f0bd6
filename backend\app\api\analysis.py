"""
数据分析API路由
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
import pandas as pd

from app.database import get_db
from app.models.user import User
from app.models.measurement import Measurement
from app.schemas.measurement import (
    MeasurementData, SPCAnalysis, SPCResult, ParetoAnalysis, ParetoData
)
from app.core.security import get_current_active_user
from app.core.exceptions import not_found_exception, bad_request_exception
from app.utils.data_utils import point_spc, generate_chart_base64, calculate_pareto_data

router = APIRouter()


@router.get("/measurements", response_model=MeasurementData)
async def get_measurements(
    file_id: int = Query(...),
    skip: int = Query(0, ge=0),
    limit: int = Query(1000, ge=1, le=10000),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取测量数据"""
    measurements = (
        db.query(Measurement)
        .filter(Measurement.file_id == file_id)
        .offset(skip)
        .limit(limit)
        .all()
    )
    
    total = db.query(Measurement).filter(Measurement.file_id == file_id).count()
    
    # 获取测量点列表
    points = (
        db.query(Measurement.point)
        .filter(Measurement.file_id == file_id)
        .distinct()
        .all()
    )
    points = [p[0] for p in points if p[0]]
    
    return MeasurementData(
        measurements=measurements,
        total=total,
        points=sorted(points)
    )


@router.post("/spc", response_model=SPCAnalysis)
async def perform_spc_analysis(
    file_ids: List[int],
    points: List[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """执行SPC分析"""
    if not file_ids:
        raise bad_request_exception("文件ID列表不能为空")
    
    # 获取测量数据
    measurements = (
        db.query(Measurement)
        .filter(Measurement.file_id.in_(file_ids))
        .all()
    )
    
    if not measurements:
        raise not_found_exception("未找到测量数据")
    
    # 转换为DataFrame
    data = []
    for m in measurements:
        data.append({
            'point': m.point,
            'deviation': m.deviation,
            'up_tol': m.up_tol,
            'low_tol': m.low_tol,
            'snplus': m.snplus,
            'nominal': m.nominal
        })
    
    df = pd.DataFrame(data)
    
    # 如果没有指定测量点，使用所有测量点
    if not points:
        points = sorted(df['point'].drop_duplicates().tolist())
    
    # 执行SPC分析
    df_spc = point_spc(df, points)
    
    # 转换结果
    results = []
    for _, row in df_spc.iterrows():
        results.append(SPCResult(
            point=row['point'],
            max_value=row['max'],
            min_value=row['min'],
            mean=row['mean'],
            std=row['std'],
            ca=row['ca'],
            cp=row['cp'],
            cpk=row['cpk'],
            sample_count=len(df[df['point'] == row['point']])
        ))
    
    return SPCAnalysis(
        results=results,
        total_points=len(results),
        analysis_time=db.func.now()
    )


@router.post("/pareto", response_model=ParetoAnalysis)
async def perform_pareto_analysis(
    file_ids: List[int],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """执行帕累托分析"""
    if not file_ids:
        raise bad_request_exception("文件ID列表不能为空")
    
    # 获取测量数据
    measurements = (
        db.query(Measurement)
        .filter(Measurement.file_id.in_(file_ids))
        .all()
    )
    
    if not measurements:
        raise not_found_exception("未找到测量数据")
    
    # 转换为DataFrame
    data = []
    for m in measurements:
        data.append({
            'point': m.point,
            'oot': m.oot or 0
        })
    
    df = pd.DataFrame(data)
    
    # 执行帕累托分析
    df_pareto = calculate_pareto_data(df)
    
    # 转换结果
    pareto_data = []
    for _, row in df_pareto.iterrows():
        pareto_data.append(ParetoData(
            point=row['point'],
            non_zero_count=row['non_zero_count'],
            percentage=row['percentage'],
            cumulative_percentage=row['cumulative_percentage']
        ))
    
    return ParetoAnalysis(
        data=pareto_data,
        total_count=df_pareto['non_zero_count'].sum(),
        analysis_time=db.func.now()
    )


@router.get("/charts/{file_id}")
async def get_charts(
    file_id: int,
    points: List[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取图表数据"""
    # 获取测量数据
    measurements = (
        db.query(Measurement)
        .filter(Measurement.file_id == file_id)
        .all()
    )
    
    if not measurements:
        raise not_found_exception("未找到测量数据")
    
    # 转换为DataFrame
    data = []
    for m in measurements:
        data.append({
            'point': m.point,
            'deviation': m.deviation,
            'up_tol': m.up_tol,
            'low_tol': m.low_tol,
            'snplus': m.snplus,
            'sn': m.sn
        })
    
    df = pd.DataFrame(data)
    
    # 如果没有指定测量点，使用所有测量点
    if not points:
        points = sorted(df['point'].drop_duplicates().tolist())
    
    # 生成图表
    charts = {}
    for point in points:
        try:
            chart_base64 = generate_chart_base64(df, point)
            charts[point] = chart_base64
        except Exception as e:
            charts[point] = f"生成图表失败: {str(e)}"
    
    return {"charts": charts}


@router.get("/points/{file_id}")
async def get_measurement_points(
    file_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """获取测量点列表"""
    points = (
        db.query(Measurement.point)
        .filter(Measurement.file_id == file_id)
        .distinct()
        .all()
    )
    
    points = [p[0] for p in points if p[0]]
    
    return {"points": sorted(points), "total": len(points)}
