# PyStation Web

PyStation Web是一个现代化的数据分析和SPC统计Web应用，从原有的PyQt5桌面应用迁移而来。

## 技术栈

### 后端
- **FastAPI**: 高性能异步Web框架
- **SQLAlchemy**: ORM数据库操作
- **Pandas**: 数据处理和分析
- **Matplotlib**: 图表生成
- **python-docx**: Word文档生成
- **openpyxl**: Excel文件处理

### 前端
- **Vue 3**: 现代化前端框架
- **TypeScript**: 类型安全
- **Element Plus**: UI组件库
- **ECharts**: 数据可视化
- **Vite**: 构建工具

## 功能特性

- ✅ 用户认证和权限管理
- ✅ 产品配置管理
- ✅ TMP文件上传和处理
- ✅ 数据分析和SPC统计
- ✅ 帕累托分析
- ✅ 图表生成和可视化
- ✅ Word报告和Excel数据导出
- ✅ 响应式Web界面

## 快速开始

### 使用Docker Compose（推荐）

1. 克隆项目
```bash
git clone <repository-url>
cd pystation-web
```

2. 启动服务
```bash
docker-compose up -d
```

3. 访问应用
- 前端: http://localhost:5173
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 手动安装

#### 后端设置

1. 进入后端目录
```bash
cd backend
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 启动后端服务
```bash
uvicorn app.main:app --reload
```

#### 前端设置

1. 进入前端目录
```bash
cd frontend
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run dev
```

## 默认登录信息

- 用户名: `admin`
- 密码: `x1`

## 项目结构

```
pystation-web/
├── backend/                    # FastAPI后端
│   ├── app/
│   │   ├── api/               # API路由
│   │   ├── core/              # 核心功能
│   │   ├── models/            # 数据模型
│   │   ├── schemas/           # Pydantic模式
│   │   ├── services/          # 业务逻辑
│   │   └── utils/             # 工具函数
│   ├── uploads/               # 上传文件存储
│   ├── reports/               # 生成的报告
│   └── requirements.txt       # Python依赖
├── frontend/                  # Vue前端
│   ├── src/
│   │   ├── api/               # API接口
│   │   ├── components/        # 组件
│   │   ├── stores/            # 状态管理
│   │   ├── types/             # TypeScript类型
│   │   └── views/             # 页面视图
│   └── package.json           # Node.js依赖
└── docker-compose.yml         # Docker部署配置
```

## API文档

启动后端服务后，可以访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 主要API端点

### 认证
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息

### 产品管理
- `GET /api/products` - 获取产品列表
- `POST /api/products` - 创建产品
- `PUT /api/products/{id}` - 更新产品
- `DELETE /api/products/{id}` - 删除产品

### 文件处理
- `POST /api/files/upload/{product_id}` - 上传文件
- `GET /api/files` - 获取文件列表
- `POST /api/files/{id}/process` - 处理文件

### 数据分析
- `GET /api/analysis/measurements` - 获取测量数据
- `POST /api/analysis/spc` - 执行SPC分析
- `POST /api/analysis/pareto` - 执行帕累托分析
- `GET /api/analysis/charts/{file_id}` - 获取图表

### 报告生成
- `POST /api/reports/generate` - 生成报告
- `GET /api/reports/download/word/{filename}` - 下载Word报告
- `GET /api/reports/download/excel/{filename}` - 下载Excel数据

## 开发指南

### 添加新的API端点

1. 在 `backend/app/api/` 中创建新的路由文件
2. 在 `backend/app/main.py` 中注册路由
3. 在前端 `frontend/src/api/` 中添加对应的API调用

### 添加新的页面

1. 在 `frontend/src/views/` 中创建新的Vue组件
2. 在 `frontend/src/router/index.ts` 中添加路由配置

### 数据库迁移

使用Alembic进行数据库迁移：

```bash
cd backend
alembic revision --autogenerate -m "描述"
alembic upgrade head
```

## 部署

### 生产环境配置

1. 修改 `backend/app/config.py` 中的配置
2. 设置环境变量：
   - `SECRET_KEY`: JWT密钥
   - `DATABASE_URL`: 数据库连接字符串
   - `ALLOWED_ORIGINS`: 允许的前端域名

3. 构建前端：
```bash
cd frontend
npm run build
```

4. 使用生产级WSGI服务器（如Gunicorn）运行后端

## 故障排除

### 常见问题

1. **文件上传失败**
   - 检查上传目录权限
   - 确认文件大小限制

2. **数据库连接错误**
   - 检查数据库配置
   - 确认数据库服务运行状态

3. **前端API调用失败**
   - 检查后端服务状态
   - 确认CORS配置

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

[MIT License](LICENSE)
