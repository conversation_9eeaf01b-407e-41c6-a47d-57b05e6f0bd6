"""
测量数据模型
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class Measurement(Base):
    """测量数据表"""
    __tablename__ = "measurements"
    
    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(Integer, ForeignKey("files.id"), nullable=False)
    
    # 测量数据字段 (对应原data_utils.py中的字段)
    dimension = Column(String(100))
    feature = Column(String(100))
    axis = Column(String(10))
    nominal = Column(Float)
    up_tol = Column(Float)
    low_tol = Column(Float)
    measurement = Column(Float)
    deviation = Column(Float)
    point = Column(String(200), index=True)
    folder = Column(String(100))
    sn = Column(String(100), index=True)
    snplus = Column(Integer)
    oot = Column(Integer, default=0)  # out of tolerance
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    file = relationship("File", back_populates="measurements")
    
    def __repr__(self):
        return f"<Measurement(id={self.id}, point='{self.point}', sn='{self.sn}')>"
