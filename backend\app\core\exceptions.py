"""
自定义异常类
"""

from fastapi import HTTPException, status


class PyStationException(Exception):
    """PyStation基础异常"""
    def __init__(self, message: str, details: str = None):
        self.message = message
        self.details = details
        super().__init__(self.message)


class ValidationError(PyStationException):
    """验证错误"""
    pass


class DataProcessingError(PyStationException):
    """数据处理错误"""
    pass


class FileProcessingError(PyStationException):
    """文件处理错误"""
    pass


class ConfigurationError(PyStationException):
    """配置错误"""
    pass


# HTTP异常快捷方式
def not_found_exception(detail: str = "Resource not found"):
    return HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=detail
    )


def bad_request_exception(detail: str = "Bad request"):
    return HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=detail
    )


def unauthorized_exception(detail: str = "Unauthorized"):
    return HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=detail,
        headers={"WWW-Authenticate": "Bearer"}
    )


def forbidden_exception(detail: str = "Forbidden"):
    return HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail=detail
    )


def internal_server_error_exception(detail: str = "Internal server error"):
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=detail
    )
