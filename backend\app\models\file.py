"""
文件模型
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, BigInteger
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class File(Base):
    """文件表"""
    __tablename__ = "files"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    filepath = Column(String(500), nullable=False)
    file_size = Column(BigInteger)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    status = Column(String(50), default="uploaded")  # uploaded, processing, processed, error
    upload_time = Column(DateTime(timezone=True), server_default=func.now())
    processed_time = Column(DateTime(timezone=True))
    
    # 关联关系
    product = relationship("Product", back_populates="files")
    measurements = relationship("Measurement", back_populates="file", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<File(id={self.id}, filename='{self.filename}')>"
