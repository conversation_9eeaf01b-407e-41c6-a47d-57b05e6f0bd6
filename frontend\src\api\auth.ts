import api from './index'
import type { User, LoginForm, Token } from '@/types'

export const authApi = {
  // 用户登录
  login: (loginForm: LoginForm): Promise<Token> => {
    return api.post('/auth/login', loginForm)
  },

  // 用户注册
  register: (registerForm: LoginForm): Promise<User> => {
    return api.post('/auth/register', registerForm)
  },

  // 获取当前用户信息
  getCurrentUser: (): Promise<User> => {
    return api.get('/auth/me')
  },

  // 用户登出
  logout: (): Promise<void> => {
    return api.post('/auth/logout')
  },
}
