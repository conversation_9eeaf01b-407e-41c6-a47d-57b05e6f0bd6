# PyStation Web Backend Dependencies
# FastAPI核心
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# 数据库
sqlalchemy>=2.0.0
alembic>=1.12.0
psycopg2-binary>=2.9.0  # PostgreSQL (可选)

# 数据处理 (保持与原项目一致)
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0

# 可视化 (后端生成图表)
matplotlib>=3.7.0

# 文档处理
python-docx>=0.8.11
openpyxl>=3.1.0

# 认证和安全
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# 文件处理
aiofiles>=23.0.0

# 配置管理
pydantic-settings>=2.0.0

# 日志和监控
loguru>=0.7.0

# 开发工具
pytest>=7.4.0
pytest-asyncio>=0.21.0
httpx>=0.25.0  # 用于测试

# 类型检查
mypy>=1.5.0

# 代码格式化
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
